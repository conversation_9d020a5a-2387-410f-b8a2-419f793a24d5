const { literal } = require('sequelize');

const {
  CashPool,
  Company,
  CashPoolBatch,
  CashPoolBatch_ParticipantPayments,
  Cash_Pool_Participants,
  CashPoolParticipantAccounts,
  CashPoolStatementData,
  TopCurrencyAccounts,
  ParticipantAccountIds,
} = require('../models');
const model = CashPoolBatch_ParticipantPayments;

function getCashPoolPayment({ cashPoolId, clientId, batchId, paymentId }) {
  return CashPoolBatch_ParticipantPayments.findOne({
    subQuery: false,
    where: { id: paymentId },
    attributes: ['id', 'interestPayable', 'interestReceivable'],
    include: [
      {
        model: Cash_Pool_Participants,
        as: 'debtor',
        required: true,
        attributes: ['isLeader', 'uniqueId'],
        where: { cashPoolId },
        include: {
          model: CashPoolParticipantAccounts,
          as: 'accounts',
          attributes: ['id', 'generateInterestStatementData'],
          // required: true,
        },
      },
      {
        model: Cash_Pool_Participants,
        as: 'creditor',
        // required: true,
        attributes: ['isLeader', 'uniqueId'],
        where: { cashPoolId },
        include: {
          model: CashPoolParticipantAccounts,
          as: 'accounts',
          attributes: ['id', 'generateInterestStatementData'],
          // required: true,
        },
      },
      {
        required: true,
        model: CashPoolBatch,
        where: { id: batchId },
        as: 'batch',
        attributes: [],
        include: {
          required: true,
          model: CashPool,
          where: { id: cashPoolId, clientId },
          as: 'cashPool',
          attributes: ['id'],
        },
      },
      {
        model: CashPoolStatementData,
        as: 'statementData',
        attributes: ['id', 'cashPoolBatchId'],
      },
    ],
  });
}

function getCashPoolPayments({ clientId, wherePayment, whereBatch, whereCreditor, whereDebtor, order, limit, offset }) {
  return CashPoolBatch_ParticipantPayments.findAll({
    subQuery: false,
    where: wherePayment,
    order,
    attributes: [
      'id',
      'interestPayable',
      'interestReceivable',
      'currency',
      'isPaid',
      [literal('COALESCE("interestPayable", "interestReceivable")'), 'interestPayableReceivable'],
    ],
    include: [
      {
        model: Cash_Pool_Participants,
        as: 'debtor',
        required: true,
        where: whereDebtor,
        attributes: ['id', 'companyId', 'uniqueId'],
        include: {
          model: Company,
          as: 'company',
          attributes: ['id', 'name'],
        },
      },
      {
        model: Cash_Pool_Participants,
        as: 'creditor',
        required: true,
        where: whereCreditor,
        attributes: ['id', 'companyId', 'uniqueId'],
        include: {
          model: Company,
          as: 'company',
          attributes: ['id', 'name'],
        },
      },
      {
        model: CashPoolParticipantAccounts,
        as: 'participantAccount',
        required: true,
        paranoid: false,
        attributes: ['id', 'debitInterestRate', 'creditInterestRate', 'generateInterestStatementData'],
        include: [
          {
            model: ParticipantAccountIds,
            as: 'externalIds',
            attributes: ['externalId', 'excludedId'],
            required: true,
            paranoid: false,
          },
          {
            model: Cash_Pool_Participants,
            required: true,
            as: 'participant',
            attributes: ['id', 'companyId', 'uniqueId'],
            include: {
              model: Company,
              as: 'company',
              attributes: ['id', 'name'],
            },
          },
          {
            model: TopCurrencyAccounts,
            required: true,
            as: 'topCurrencyAccount',
            attributes: ['name', 'interestType', 'overnightRate'],
          },
        ],
      },
      {
        model: CashPoolBatch,
        where: whereBatch,
        as: 'batch',
        required: true,
        attributes: ['id', 'status', 'startDate', 'endDate'],
        include: {
          model: CashPool,
          required: true,
          as: 'cashPool',
          attributes: ['id', 'leaderId', 'name', 'interestType'],
          where: { clientId },
          include: {
            model: Company,
            as: 'leader',
            attributes: ['id', 'name'],
          },
        },
      },
      {
        model: CashPoolStatementData,
        as: 'statementData',
        attributes: ['id', 'cashPoolBatchId'],
      },
    ],
    limit,
    offset,
  });
}

function getTotalPaymentsCount({ clientId, whereBatch }) {
  return CashPoolBatch_ParticipantPayments.count({
    include: {
      model: CashPoolBatch,
      where: whereBatch,
      as: 'batch',
      required: true,
      include: {
        model: CashPool,
        required: true,
        as: 'cashPool',
        where: { clientId },
      },
    },
  });
}

function getPaidPaymentsCount({ clientId, whereBatch }) {
  return CashPoolBatch_ParticipantPayments.count({
    where: { isPaid: true },
    include: {
      model: CashPoolBatch,
      where: whereBatch,
      as: 'batch',
      required: true,
      include: {
        model: CashPool,
        required: true,
        as: 'cashPool',
        where: { clientId },
      },
    },
  });
}

function createCashPoolPayment(data) {
  return model.create(data);
}

function updateCashPoolPayment(where, data) {
  return model.update(data, { where });
}

module.exports = {
  getCashPoolPayment,
  getCashPoolPayments,
  getPaidPaymentsCount,
  getTotalPaymentsCount,
  createCashPoolPayment,
  updateCashPoolPayment,
};
