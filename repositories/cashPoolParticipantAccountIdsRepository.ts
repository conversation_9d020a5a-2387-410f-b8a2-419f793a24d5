import models from '../models';
import { DbParticipantAccountIdType, ParticipantAccountIdType } from '../types';

const { ParticipantAccountIds, CashPoolParticipantAccounts, Cash_Pool_Participants, CashPool } = models;

function bulkCreate(
  participantAccountIds: Array<ParticipantAccountIdType>,
): Promise<Array<DbParticipantAccountIdType>> {
  return ParticipantAccountIds.bulkCreate(participantAccountIds);
}

function createIfNotExists({ cashPoolAccountId, externalId }: ParticipantAccountIdType) {
  return ParticipantAccountIds.findOrCreate({
    where: { cashPoolAccountId, externalId },
    defaults: { cashPoolAccountId, externalId },
  });
}

function getOne({ where }: any) {
  return ParticipantAccountIds.findOne({ where, include: { model: CashPoolParticipantAccounts, as: 'account' } });
}

function getExternalIdsByCashPoolId(cashPoolId: number): Promise<Array<{ externalId: string }>> {
  return ParticipantAccountIds.findAll({
    attributes: ['externalId', 'excludedId'],
    raw: true,
    include: {
      model: CashPoolParticipantAccounts,
      as: 'account',
      attributes: [],
      required: true,
      include: {
        model: Cash_Pool_Participants,
        as: 'participant',
        attributes: [],
        required: true,
        include: {
          model: CashPool,
          as: 'cashPool',
          where: { id: cashPoolId },
          attributes: [],
          required: true,
        },
      },
    },
  });
}

function deleteByExternalId(externalId: string | Array<string>): Promise<number> {
  return ParticipantAccountIds.destroy({ where: { externalId } });
}

function deleteByCashPoolAccountId(cashPoolAccountId: string | Array<string>): Promise<number> {
  return ParticipantAccountIds.destroy({ where: { cashPoolAccountId } });
}

async function getLeaderParticipantExternalId(cashPoolId: number): Promise<string> {
  const { externalId } = await ParticipantAccountIds.findOne({
    attributes: ['externalId', 'excludedId'],
    include: {
      model: CashPoolParticipantAccounts,
      as: 'account',
      required: true,
      attributes: [],
      include: {
        model: Cash_Pool_Participants,
        as: 'participant',
        where: { cashPoolId, isLeader: true },
        required: true,
        attributes: [],
      },
    },
  });

  return externalId;
}

export {
  bulkCreate,
  createIfNotExists,
  deleteByCashPoolAccountId,
  deleteByExternalId,
  getExternalIdsByCashPoolId,
  getLeaderParticipantExternalId,
  getOne,
};
