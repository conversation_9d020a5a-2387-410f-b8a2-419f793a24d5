'use strict';
import { QueryInterface } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.addColumn('ParticipantAccountIds', 'excludedIds', {
        type: Sequelize.STRING,
        allowNull: true,
      });
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async () => {
      await queryInterface.removeColumn('ParticipantAccountIds', 'excludedIds');
    });
  },
};
